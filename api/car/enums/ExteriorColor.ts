import { useI18n } from 'vue-i18n'

const exteriorColorValues = [
  'white',
  'black',
  'gray',
  'silver',
  'red',
  'blue',
  'yellow',
  'green',
  'beige',
  'brown',
  'orange',
  'gold',
] as const

export const ExteriorColor = exteriorColorValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof exteriorColorValues)[number],
    (typeof exteriorColorValues)[number]
  >,
)

export const useLocalizedExteriorColor = () => {
  const { t } = useI18n()
  return exteriorColorValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.exterior_color.${value}`)
      return acc
    },
    {} as Record<(typeof exteriorColorValues)[number], string>,
  )
}

export type ExteriorColorType = (typeof exteriorColorValues)[number]
