// Example usage of the enum files with automatic type detection
import {
  City,
  useLocalizedCity,
  useLocalizedCityValue,
  Status,
  useLocalizedStatusValue
} from './index'
import { useLocalizedEnumValue } from './utils'

// Example in a Vue component
export function useEnumExample() {
  // Get the enum values (no more Enum const objects!)
  const selectedCity: City = 'damascus'
  const selectedStatus: Status = 'new'

  // Get localized versions using composables
  const localizedCities = useLocalizedCity()

  // Get individual localized values - multiple ways:

  // 1. Using specific enum helper
  const cityName1 = useLocalizedCityValue(selectedCity) // 'دمشق'
  const statusName1 = useLocalizedStatusValue(selectedStatus) // 'جديد'

  // 2. Using generic helper with automatic type detection
  const cityName2 = useLocalizedEnumValue('damascus') // 'دمشق'
  const statusName2 = useLocalizedEnumValue('new') // 'جديد'

  // 3. Using the full enum object
  const cityName3 = localizedCities[selectedCity] // 'دمشق'

  return {
    selectedCity,
    selectedStatus,
    cityName1,
    statusName1,
    cityName2,
    statusName2,
    cityName3,
    localizedCities
  }
}

// Example for creating select options
export function useCityOptions() {
  const localizedCities = useLocalizedCity()

  return Object.entries(localizedCities).map(([value, label]) => ({
    value,
    label
  }))
}

// Example showing the power of automatic detection
export function useGenericEnumExample() {
  // These all work automatically without specifying the enum type!
  const damascus = useLocalizedEnumValue('damascus') // 'دمشق'
  const newStatus = useLocalizedEnumValue('new') // 'جديد'
  const suv = useLocalizedEnumValue('suv') // 'جيب | SUV'
  const petrol = useLocalizedEnumValue('petrol') // 'بنزين'

  return { damascus, newStatus, suv, petrol }
}
