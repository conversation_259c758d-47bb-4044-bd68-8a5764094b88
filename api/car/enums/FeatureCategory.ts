import { useI18n } from 'vue-i18n'

const featureCategoryValues = [
  'safety_and_security',
  'comfort',
  'exterior',
  'entertainment',
  'other',
] as const

export const FeatureCategory = featureCategoryValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof featureCategoryValues)[number],
    (typeof featureCategoryValues)[number]
  >,
)

export const useLocalizedFeatureCategory = () => {
  const { t } = useI18n()
  return featureCategoryValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.feature_category.${value}`)
      return acc
    },
    {} as Record<(typeof featureCategoryValues)[number], string>,
  )
}

export type FeatureCategoryType = (typeof featureCategoryValues)[number]
