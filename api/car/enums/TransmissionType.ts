import { useI18n } from 'vue-i18n'

const transmissionTypeValues = [
  'automatic',
  'manual',
  'tiptronic',
  'cvt',
  'dct',
] as const

export const TransmissionType = transmissionTypeValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof transmissionTypeValues)[number],
    (typeof transmissionTypeValues)[number]
  >,
)

export const useLocalizedTransmissionType = () => {
  const { t } = useI18n()
  return transmissionTypeValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.transmission_type.${value}`)
      return acc
    },
    {} as Record<(typeof transmissionTypeValues)[number], string>,
  )
}

export type TransmissionTypeType = (typeof transmissionTypeValues)[number]
