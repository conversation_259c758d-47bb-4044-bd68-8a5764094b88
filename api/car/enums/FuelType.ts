import { useI18n } from 'vue-i18n'

const fuelTypeValues = ['petrol', 'diesel', 'electric', 'hybrid'] as const

export const FuelType = fuelTypeValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof fuelTypeValues)[number],
    (typeof fuelTypeValues)[number]
  >,
)

export const useLocalizedFuelType = () => {
  const { t } = useI18n()
  return fuelTypeValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.fuel_type.${value}`)
      return acc
    },
    {} as Record<(typeof fuelTypeValues)[number], string>,
  )
}

export type FuelTypeType = (typeof fuelTypeValues)[number]
